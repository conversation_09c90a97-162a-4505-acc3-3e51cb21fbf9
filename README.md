<div align=center>
  <h1 align="center">Authorization</h1>
  <p align="center">高性能分布式授权管理系统</p>
</div>

<div align=center>
  <p align="center">
    <a href="https://gitlab.com/li-bao-jia">
      <img src="https://img.shields.io/badge/go-1.22.6-blue" alt="Go Version">
    </a>
    <a href="#">
      <img src="https://img.shields.io/badge/redis-latest-red" alt="Redis">
    </a>
    <a href="#">
      <img src="https://img.shields.io/badge/mongodb-latest-green" alt="MongoDB">
    </a>
    <a href="#">
      <img src="https://img.shields.io/badge/rabbitmq-latest-orange" alt="RabbitMQ">
    </a>
  </p>
</div>

## 项目介绍

这是一个基于Go语言开发的高性能分布式授权管理系统，主要功能包括：

- 用户认证与授权管理
- 基于角色的访问控制（RBAC）
- OAuth2.0 和 JWT 支持
- API 权限管理
- 操作审计与日志
- 多租户支持

系统采用现代化架构设计，使用Redis进行缓存加速，RabbitMQ处理异步消息，MongoDB存储审计日志，保证系统的高可用性、可扩展性和安全性。

## 环境要求

```
- IDE推荐：Goland
- Go版本 >= v1.22
- Redis >= 6.0
- MongoDB >= 4.4
- RabbitMQ >= 3.8
```

## 系统架构

![系统架构图](docs/images/architecture.png)

授权系统采用领域驱动设计(DDD)的思想构建，主要分为以下几层：

- **接口层**：处理HTTP/gRPC请求和响应
- **应用层**：协调领域对象完成用户操作，处理命令和查询
- **领域层**：包含核心业务逻辑和规则，是系统的核心
- **基础设施层**：提供技术支持，如数据库访问、缓存、消息队列等

## 目录结构

```
.
├── cmd                     # 应用程序入口点
│   ├── api                 # API服务入口
│   │   └── main.go
│   ├── worker              # 后台工作进程入口
│   │   └── main.go
│   └── migration           # 数据库迁移工具
│       └── main.go
├── internal                # 内部代码，不对外暴露
│   ├── domain              # 领域层 - 核心业务逻辑
│   │   ├── auth            # 认证领域
│   │   ├── user            # 用户领域
│   │   ├── role            # 角色领域
│   │   ├── permission      # 权限领域
│   │   ├── tenant          # 租户领域
│   │   └── audit           # 审计领域
│   ├── application         # 应用层 - 协调领域对象
│   │   ├── service         # 应用服务
│   │   ├── dto             # 数据传输对象
│   │   ├── command         # 命令处理器
│   │   └── query           # 查询处理器
│   ├── infrastructure      # 基础设施层
│   │   ├── persistence     # 持久化
│   │   │   ├── mysql       # MySQL实现
│   │   │   └── mongodb     # MongoDB实现
│   │   ├── cache           # 缓存(Redis)
│   │   ├── messaging       # 消息队列(RabbitMQ)
│   │   └── repository      # 仓储实现
│   └── interfaces          # 接口层
│       ├── api             # API接口
│       ├── event           # 事件处理
│       └── job             # 后台任务
├── pkg                     # 可重用的公共包
│   ├── auth                # 认证工具
│   ├── cache               # 缓存工具
│   ├── errors              # 错误处理
│   ├── logger              # 日志工具
│   ├── middleware          # 中间件
│   └── utils               # 通用工具
├── api                     # API定义
│   ├── http                # HTTP API
│   │   ├── v1              # V1版本
│   │   └── v2              # V2版本
│   └── grpc                # gRPC API
├── web                     # Web前端
│   ├── admin               # 管理后台
│   └── portal              # 用户门户
├── config                  # 配置文件
│   ├── config.go
│   └── config.yaml
├── scripts                 # 脚本文件
│   ├── setup.sh            # 环境设置
│   └── test.sh             # 测试脚本
├── deploy                  # 部署配置
│   ├── docker              # Docker配置
│   └── kubernetes          # Kubernetes配置
├── docs                    # 文档
│   ├── api                 # API文档
│   ├── images              # 图片资源
│   └── guides              # 使用指南
├── tests                   # 测试
│   ├── unit                # 单元测试
│   ├── integration         # 集成测试
│   └── e2e                 # 端到端测试
├── Dockerfile
├── docker-compose.yml      # 本地开发环境
├── README.md
├── .env.example            # 环境变量示例
├── go.mod
└── go.sum
```

## 技术栈

- **后端框架**: Go 1.22+, Gin
- **数据存储**:
  - MySQL/PostgreSQL (主数据库)
  - Redis (缓存、速率限制)
  - MongoDB (审计日志存储)
- **消息队列**: RabbitMQ
- **认证**: JWT, OAuth2.0
- **API文档**: Swagger/OpenAPI
- **监控**: Prometheus, Grafana
- **部署**: Docker, Kubernetes

## 核心功能

### 认证与授权
- 多种认证方式支持 (用户名/密码, OAuth2.0, LDAP)
- JWT令牌管理
- 会话控制

### 权限管理
- 基于角色的访问控制 (RBAC)
- 细粒度权限控制
- 动态权限策略

### 多租户
- 租户隔离
- 租户特定配置
- 跨租户操作

### 审计与日志
- 操作审计
- 安全事件记录
- 合规报告

## 安装与配置

### 1. 克隆项目

```sh
git clone --recurse-submodules https://gitlab.com/li-bao-jia/authorization.git
cd authorization
```

### 2. 环境配置

复制环境变量示例文件并修改：

```sh
cp .env.example .env
# 编辑.env文件，配置数据库、Redis、MongoDB和RabbitMQ连接信息
```

### 3. 启动依赖服务

使用Docker Compose启动开发环境：

```sh
docker-compose up -d redis mongodb rabbitmq
```

### 4. 构建并运行

```sh
go mod tidy
go run cmd/server/main.go
```

## API文档

系统提供了完整的API文档，使用Swagger/OpenAPI规范：

```sh
# 启动服务后访问Swagger文档
http://localhost:8080/swagger/index.html
```

## 开发指南

### 代码规范

项目遵循Go标准代码规范和最佳实践：

```sh
# 运行代码检查
go vet ./...
golangci-lint run
```

### 测试

```sh
# 运行单元测试
go test ./... -v

# 运行集成测试
go test ./tests/integration/... -v

# 运行性能测试
go test ./tests/performance/... -bench=.
```

### 数据库迁移

```sh
# 创建新迁移
go run cmd/migration/main.go create add_new_feature

# 应用迁移
go run cmd/migration/main.go up
```

## Redis集成

系统使用Redis进行以下功能：

- 令牌缓存与验证
- 速率限制
- 分布式锁
- 会话管理

配置示例：

```yaml
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
  pool_size: 10
```

## MongoDB集成

MongoDB主要用于存储审计日志和操作记录，支持高效的日志查询和分析：

```yaml
mongodb:
  uri: "mongodb://localhost:27017"
  database: "auth_logs"
  collections:
    audit: "audit_logs"
    operations: "operation_logs"
    security: "security_events"
```

## 消息队列集成

系统使用RabbitMQ处理异步任务，如：

- 发送通知
- 异步审计日志记录
- 系统事件处理

```yaml
rabbitmq:
  host: "localhost"
  port: 5672
  username: "guest"
  password: "guest"
  vhost: "/"
  queues:
    notifications: "auth.notifications"
    audit_logs: "auth.audit_logs"
    events: "auth.events"
```

## 插件系统

授权系统支持插件扩展，可以通过插件机制扩展系统功能：

```go
// 插件示例
package main

import "github.com/your-org/authorization/pkg/plugins"

func main() {
    // 实现插件接口
    p := MyPlugin{}
    plugins.Register("my-plugin", p)
}
```

## 路线图

- [ ] 多因素认证支持
- [ ] OAuth2.0提供者集成
- [ ] 高级审计报告
- [ ] 实时监控仪表板
- [ ] 基于AI的异常检测

## 贡献指南

欢迎贡献代码、报告问题或提出改进建议。请遵循以下步骤：

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

[MIT](LICENSE)

## 联系方式

如有问题或建议，请通过以下方式联系我们：

- 邮箱：<EMAIL>
- 问题跟踪：[GitLab Issues](https://gitlab.com/li-bao-jia/authorization/-/issues)
