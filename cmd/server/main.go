package server

type MySQLConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	DBName   string `yaml:"dbname"`
}

func main() {

	// 启动 Elasticsearch 服务
	//esClient, err := es.StartClient()
	//if err != nil {
	//	log.Fatalf("Error initializing Elasticsearch: %s", err)
	//}

	// 启动消息队列生产者
	//mqProducer, err := mq.StartProducer()
	//if err != nil {
	//	log.Fatalf("Error initializing MQ: %s", err)
	//}

	// 启动 HTTP 服务
	//server := httpserver.NewServer(esClient, mqProducer)
	//if err := server.Start(":8080"); err != nil {
	//	log.Fatalf("Failed to start HTTP server: %s", err)
	//}
}
