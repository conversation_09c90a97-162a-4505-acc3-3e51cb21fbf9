package plugins

import "fmt"

type Plugin interface{}

type Plugins map[string]Plugin

var plugins = make(Plugins)

func Register(key string, plugin Plugin) {
	if _, ok := plugins[key]; ok {
		fmt.Println("plugin already registered")
	}
	plugins[key] = plugin
}

func GetPlugins() Plugins {
	return plugins
}

func GetPlugin(key string) (p Plugin, err error) {
	var ok bool
	if p, ok = plugins[key]; !ok {
		return p, fmt.Errorf("plugin '%s' not found", key)
	}

	return
}
